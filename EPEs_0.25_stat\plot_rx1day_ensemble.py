#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RX1-day集合统计结果绘图脚本
读取CSV结果文件并绘制四种情景的时间序列图在一张图中

作者: 自动生成
日期: 2025-08-26
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体和图形参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300

def setup_paths():
    """设置输入输出路径"""
    # CSV文件路径
    input_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth2"
    ensemble_csv = os.path.join(input_dir, "rx1day_ensemble_statistics.csv")
    
    # 输出路径
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth3"
    
    return ensemble_csv, output_dir

def load_ensemble_data(csv_file):
    """加载集合统计数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        logger.info(f"成功加载数据: {csv_file}")
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"列名: {list(df.columns)}")
        
        # 检查数据
        scenarios = df['Scenario'].unique()
        years = sorted(df['Year'].unique())
        logger.info(f"情景: {scenarios}")
        logger.info(f"年份范围: {min(years)} - {max(years)}")
        
        return df, scenarios, years
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise

def create_combined_plot(df, scenarios, years, output_path):
    """创建四种情景合并的时间序列图"""
    # 设置图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # 定义颜色和样式
    colors = {
        'ssp126': '#1f77b4',  # 蓝色
        'ssp245': '#ff7f0e',  # 橙色
        'ssp370': '#2ca02c',  # 绿色
        'ssp585': '#d62728'   # 红色
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    # 为每个情景绘制数据
    for scenario in scenarios:
        if scenario in colors:
            # 筛选当前情景的数据
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            # 提取数据
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            # 处理NaN值
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 绘制置信区间（阴影）
                ax.fill_between(valid_years, valid_p05, valid_p95, 
                               color=colors[scenario], alpha=0.2, 
                               label=f'{scenario_labels[scenario]} 不确定性范围')
                
                # 绘制主线（集合平均）
                ax.plot(valid_years, valid_mean, color=colors[scenario], linewidth=2.5, 
                       label=f'{scenario_labels[scenario]} 集合平均', marker='o', markersize=5,
                       markerfacecolor='white', markeredgecolor=colors[scenario], markeredgewidth=1.5)
                
                # 绘制上下边界线
                ax.plot(valid_years, valid_p95, color=colors[scenario], 
                       linestyle='--', alpha=0.8, linewidth=1.2)
                ax.plot(valid_years, valid_p05, color=colors[scenario], 
                       linestyle='--', alpha=0.8, linewidth=1.2)
                
                logger.info(f"{scenario}: 绘制了 {len(valid_years)} 个数据点")
            else:
                logger.warning(f"{scenario}: 没有有效数据点")
    
    # 设置图形属性
    ax.set_title('RX1-day增长率时间序列 (所有情景集合统计)', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('年份', fontsize=14)
    ax.set_ylabel('增长率 (%)', fontsize=14)
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置x轴
    ax.set_xlim(2020, 2105)
    ax.set_xticks(range(2025, 2101, 10))
    ax.tick_params(axis='both', which='major', labelsize=12)
    
    # 添加零线
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.6, linewidth=1)
    
    # 设置图例
    handles, labels = ax.get_legend_handles_labels()
    # 重新排序图例，让主线在前，不确定性范围在后
    main_handles = [h for h, l in zip(handles, labels) if '集合平均' in l]
    main_labels = [l for l in labels if '集合平均' in l]
    uncertainty_handles = [h for h, l in zip(handles, labels) if '不确定性范围' in l]
    uncertainty_labels = [l for l in labels if '不确定性范围' in l]
    
    # 创建两列图例
    legend1 = ax.legend(main_handles, main_labels, loc='upper left', 
                       fontsize=11, frameon=True, fancybox=True, shadow=True)
    legend2 = ax.legend(uncertainty_handles, uncertainty_labels, loc='lower right', 
                       fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax.add_artist(legend1)  # 添加第一个图例回去
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    logger.info(f"合并时间序列图已保存到: {output_path}")

def create_subplot_version(df, scenarios, years, output_path):
    """创建子图版本（备选方案）"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    colors = {
        'ssp126': '#1f77b4',
        'ssp245': '#ff7f0e', 
        'ssp370': '#2ca02c',
        'ssp585': '#d62728'
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5',
        'ssp370': 'SSP3-7.0', 
        'ssp585': 'SSP5-8.5'
    }
    
    for i, scenario in enumerate(scenarios):
        if i < len(axes) and scenario in colors:
            ax = axes[i]
            
            # 筛选当前情景的数据
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            # 提取数据
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            # 处理NaN值
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 绘制置信区间
                ax.fill_between(valid_years, valid_p05, valid_p95, 
                               color=colors[scenario], alpha=0.3, 
                               label='P05-P95 范围')
                
                # 绘制主线
                ax.plot(valid_years, valid_mean, color=colors[scenario], linewidth=2, 
                       label='集合平均', marker='o', markersize=4)
                
                # 绘制边界线
                ax.plot(valid_years, valid_p95, color=colors[scenario], 
                       linestyle='--', alpha=0.7, linewidth=1)
                ax.plot(valid_years, valid_p05, color=colors[scenario], 
                       linestyle='--', alpha=0.7, linewidth=1)
            
            # 设置子图属性
            ax.set_title(f'{scenario_labels[scenario]} RX1-day增长率', fontsize=14, fontweight='bold')
            ax.set_xlabel('年份', fontsize=12)
            ax.set_ylabel('增长率 (%)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=10)
            ax.set_xlim(2020, 2105)
            ax.set_xticks(range(2025, 2101, 15))
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=0.8)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    logger.info(f"子图版本已保存到: {output_path}")

def generate_summary_statistics(df, scenarios):
    """生成汇总统计信息"""
    logger.info("=" * 50)
    logger.info("汇总统计信息:")
    
    for scenario in scenarios:
        scenario_data = df[df['Scenario'] == scenario]
        
        # 计算有效数据点
        valid_mean = scenario_data['Ensemble_Mean'].dropna()
        
        if len(valid_mean) > 0:
            logger.info(f"\n{scenario.upper()}:")
            logger.info(f"  有效数据点: {len(valid_mean)}")
            logger.info(f"  增长率范围: {valid_mean.min():.2f}% - {valid_mean.max():.2f}%")
            logger.info(f"  平均增长率: {valid_mean.mean():.2f}%")
            
            # 2100年的值
            data_2100 = scenario_data[scenario_data['Year'] == 2100]
            if not data_2100.empty and not pd.isna(data_2100['Ensemble_Mean'].iloc[0]):
                mean_2100 = data_2100['Ensemble_Mean'].iloc[0]
                p95_2100 = data_2100['P95'].iloc[0]
                p05_2100 = data_2100['P05'].iloc[0]
                logger.info(f"  2100年预估: {mean_2100:.2f}% ({p05_2100:.2f}% - {p95_2100:.2f}%)")
        else:
            logger.info(f"\n{scenario.upper()}: 无有效数据")
    
    logger.info("=" * 50)

def main():
    """主函数"""
    logger.info("开始RX1-day集合统计结果绘图...")

    # 设置路径
    ensemble_csv, output_dir = setup_paths()

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"输出目录: {output_dir}")

    # 检查输入文件是否存在
    if not os.path.exists(ensemble_csv):
        logger.error(f"输入文件不存在: {ensemble_csv}")
        return
    
    # 加载数据
    df, scenarios, years = load_ensemble_data(ensemble_csv)
    
    # 生成汇总统计
    generate_summary_statistics(df, scenarios)
    
    # 创建合并时间序列图
    logger.info("创建合并时间序列图...")
    combined_output = os.path.join(output_dir, "rx1day_combined_timeseries.png")
    create_combined_plot(df, scenarios, years, combined_output)
    
    # 创建子图版本（可选）
    logger.info("创建子图版本...")
    subplot_output = os.path.join(output_dir, "rx1day_subplot_timeseries.png")
    create_subplot_version(df, scenarios, years, subplot_output)
    
    logger.info("绘图完成!")

if __name__ == "__main__":
    main()
