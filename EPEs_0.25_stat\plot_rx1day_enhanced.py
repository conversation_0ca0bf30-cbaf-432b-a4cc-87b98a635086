#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RX1-day集合统计结果优化绘图脚本
创建更加美观和清晰的时间序列图

作者: 自动生成
日期: 2025-08-26
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import make_interp_spline
import seaborn as sns
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置图形样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# 设置中文字体和高质量参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 11
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.alpha'] = 0.3

def setup_paths():
    """设置输入输出路径"""
    input_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth2"
    ensemble_csv = os.path.join(input_dir, "rx1day_ensemble_statistics.csv")
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth4"
    return ensemble_csv, output_dir

def load_ensemble_data(csv_file):
    """加载集合统计数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        logger.info(f"成功加载数据: {csv_file}")
        scenarios = df['Scenario'].unique()
        years = sorted(df['Year'].unique())
        return df, scenarios, years
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise

def smooth_curve(x, y, num_points=100):
    """创建平滑曲线"""
    if len(x) < 3:
        return x, y
    
    # 创建更密集的x点用于平滑
    x_smooth = np.linspace(x.min(), x.max(), num_points)
    
    # 使用三次样条插值
    try:
        spl = make_interp_spline(x, y, k=3)
        y_smooth = spl(x_smooth)
        return x_smooth, y_smooth
    except:
        # 如果插值失败，返回原始数据
        return x, y

def create_enhanced_plot(df, scenarios, years, output_path):
    """创建优化的时间序列图"""
    # 设置图形大小和样式
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 定义更加美观的颜色方案
    colors = {
        'ssp126': '#2E86AB',  # 深蓝色
        'ssp245': '#A23B72',  # 深紫红色
        'ssp370': '#F18F01',  # 橙色
        'ssp585': '#C73E1D'   # 深红色
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    # 为每个情景绘制数据
    for i, scenario in enumerate(scenarios):
        if scenario in colors:
            # 筛选当前情景的数据
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            # 提取数据
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            # 处理NaN值
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 创建平滑曲线
                years_smooth, mean_smooth = smooth_curve(valid_years, valid_mean)
                years_smooth_p95, p95_smooth = smooth_curve(valid_years, valid_p95)
                years_smooth_p05, p05_smooth = smooth_curve(valid_years, valid_p05)
                
                # 绘制置信区间（更淡的颜色）
                ax.fill_between(years_smooth_p95, p05_smooth, p95_smooth, 
                               color=colors[scenario], alpha=0.15, 
                               label=f'{scenario_labels[scenario]} 不确定性范围')
                
                # 绘制平滑的主线
                ax.plot(years_smooth, mean_smooth, color=colors[scenario], 
                       linewidth=3, label=f'{scenario_labels[scenario]}', 
                       alpha=0.9, zorder=5)
                
                # 绘制原始数据点（实心圆点）
                ax.scatter(valid_years, valid_mean, color=colors[scenario], 
                          s=60, alpha=0.8, zorder=6, edgecolors='white', 
                          linewidth=1.5)
                
                logger.info(f"{scenario}: 绘制了 {len(valid_years)} 个数据点")
    
    # 优化图形外观
    ax.set_title('RX1-day极端降水增长率预估', fontsize=18, fontweight='bold', 
                pad=25, color='#2C3E50')
    ax.set_xlabel('年份', fontsize=14, fontweight='bold', color='#34495E')
    ax.set_ylabel('增长率 (%)', fontsize=14, fontweight='bold', color='#34495E')
    
    # 设置更精细的网格
    ax.grid(True, alpha=0.2, linestyle='-', linewidth=0.8)
    ax.set_axisbelow(True)
    
    # 设置坐标轴范围和刻度
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-5, max(df['P95'].max() * 1.1, 40))
    ax.set_xticks(range(2025, 2101, 15))
    ax.tick_params(axis='both', which='major', labelsize=11, colors='#2C3E50')
    
    # 添加更明显的零线
    ax.axhline(y=0, color='#34495E', linestyle='-', alpha=0.8, linewidth=1.5)
    
    # 优化图例
    handles, labels = ax.get_legend_handles_labels()
    # 只显示主线的图例，不显示不确定性范围
    main_handles = [h for h, l in zip(handles, labels) if '不确定性范围' not in l]
    main_labels = [l for l in labels if '不确定性范围' not in l]
    
    legend = ax.legend(main_handles, main_labels, loc='upper left', 
                      fontsize=12, frameon=True, fancybox=True, 
                      shadow=True, framealpha=0.9, edgecolor='#BDC3C7')
    legend.get_frame().set_facecolor('#FFFFFF')
    
    # 移除顶部和右侧的边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#BDC3C7')
    ax.spines['bottom'].set_color('#BDC3C7')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    logger.info(f"优化版时间序列图已保存到: {output_path}")

def create_minimal_plot(df, scenarios, years, output_path):
    """创建极简风格的图表"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # 极简配色方案
    colors = {
        'ssp126': '#3498DB',  # 蓝色
        'ssp245': '#E67E22',  # 橙色
        'ssp370': '#27AE60',  # 绿色
        'ssp585': '#E74C3C'   # 红色
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    for scenario in scenarios:
        if scenario in colors:
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 极简的置信区间
                ax.fill_between(valid_years, valid_p05, valid_p95, 
                               color=colors[scenario], alpha=0.1)
                
                # 平滑曲线
                years_smooth, mean_smooth = smooth_curve(valid_years, valid_mean)
                ax.plot(years_smooth, mean_smooth, color=colors[scenario], 
                       linewidth=2.5, label=scenario_labels[scenario])
    
    # 极简样式设置
    ax.set_title('RX1-day增长率预估', fontsize=16, pad=20)
    ax.set_xlabel('年份', fontsize=12)
    ax.set_ylabel('增长率 (%)', fontsize=12)
    
    ax.grid(True, alpha=0.1)
    ax.set_xlim(2020, 2105)
    ax.set_xticks(range(2030, 2101, 20))
    
    # 移除边框
    for spine in ax.spines.values():
        spine.set_visible(False)
    
    # 简洁图例
    ax.legend(frameon=False, loc='upper left')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    logger.info(f"极简版时间序列图已保存到: {output_path}")

def main():
    """主函数"""
    logger.info("开始创建优化版RX1-day时间序列图...")
    
    # 设置路径
    ensemble_csv, output_dir = setup_paths()
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查输入文件
    if not os.path.exists(ensemble_csv):
        logger.error(f"输入文件不存在: {ensemble_csv}")
        return
    
    # 加载数据
    df, scenarios, years = load_ensemble_data(ensemble_csv)
    
    # 创建优化版图表
    logger.info("创建优化版时间序列图...")
    enhanced_output = os.path.join(output_dir, "rx1day_enhanced_timeseries.png")
    create_enhanced_plot(df, scenarios, years, enhanced_output)
    
    # 创建极简版图表
    logger.info("创建极简版时间序列图...")
    minimal_output = os.path.join(output_dir, "rx1day_minimal_timeseries.png")
    create_minimal_plot(df, scenarios, years, minimal_output)
    
    logger.info("优化绘图完成!")

if __name__ == "__main__":
    main()
