clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 设置输入和输出目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_daily_mean';
yz_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_yz';
output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs';

% 如果输出目录不存在则创建
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% 读取阈值数据
yz_file = fullfile(yz_dir, 'basin_precipitation_thresholds2.csv');
yz_table = readtable(yz_file);

% 获取阈值数据
pre_yz = table2array(yz_table(:,{'P90','P95','P99'}));

fprintf('已读取流域降水阈值数据\n');

% 定义SSP情景和年份范围
ssps = {'ssp126', 'ssp245', 'ssp370', 'ssp585'};
models = {'MME', 'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};


% 循环处理每个SSP情景
for s = 1:length(ssps)
    ssp = ssps{s};
    
    % 循环处理每个模型
    for m = 1:length(models)
        model = models{m};
        
        % 构建模型输入输出目录
        model_input_dir = fullfile(input_dir, ssp, model);
        model_output_dir = fullfile(output_dir, ssp, model);
        
        % 如果输出目录不存在则创建
        if ~exist(model_output_dir, 'dir')
            mkdir(model_output_dir);
        end
        
        % 循环处理每一年
        for y = 2021:2100
            % 构建输入文件名
            input_file = fullfile(model_input_dir, sprintf('basin_daily_means_%d.csv', y));
            
            % 读取数据
            data = readtable(input_file);
            daily_data = table2array(data(:,2:end)); % 跳过第一列BasinID
            
            % 计算极端降水指标
            [outdata] = compute_EX_pr_mon_8index_basins(daily_data, pre_yz);
            
            % 创建表头
            headers = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};
            
            % 将数据转换为表格
            T = array2table(outdata, 'VariableNames', headers);
            
            % 构建输出文件路径
            output_file = fullfile(model_output_dir, sprintf('EPEs_%d.csv', y));
            
            % 将表格写入CSV文件
            writetable(T, output_file);
            
            fprintf('已完成%s_%s_%d年数据处理\n', model, ssp, y);
        end
    end
end


clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 定义模型
models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};

% 定义SSP情景
ssps = {'ssp126','ssp245','ssp370','ssp585'};

% 循环处理每个模型
for m_idx = 1:length(models)
    model = models{m_idx};
    
    % 读取该模型的校正系数
    correction_file = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_EPEs_models/%s/correction_factors.csv', model);
    correction_table = readtable(correction_file);
    correction_factors = table2array(correction_table);
    
    % 循环处理每个SSP情景
    for s_idx = 1:length(ssps)
        ssp = ssps{s_idx};
        
        % 设置输入输出路径
        input_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs/%s/%s', ssp, model);
        output_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs_corrected/%s/%s', ssp, model);
        
        % 如果输出目录不存在则创建
        if ~exist(output_dir, 'dir')
            mkdir(output_dir);
        end
        
        % 循环处理每一年
        for year = 2021:2100
            % 读取原始EPEs数据
            input_file = fullfile(input_dir, sprintf('EPEs_%d.csv', year));
            data_table = readtable(input_file);
            data = table2array(data_table);
            
            % 应用校正系数
            corrected_data = data .* correction_factors;
            
            % 创建校正后的表格
            corrected_table = array2table(corrected_data, 'VariableNames', HW_indexs);
            
            % 保存校正后的数据
            output_file = fullfile(output_dir, sprintf('EPEs_%d.csv', year));
            writetable(corrected_table, output_file);
            
            fprintf('已完成%s模式%s情景%d年数据的校正\n', model, ssp, year);
        end
    end
end

fprintf('已完成所有模式和情景数据的校正\n');



clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 定义SSP情景和年份范围
ssps = {'ssp126', 'ssp245', 'ssp370', 'ssp585'};
models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};

% 设置输入目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs_corrected';

% 循环处理每个SSP情景
for s = 1:length(ssps)
    ssp = ssps{s};
    
    % 初始化存储所有模型数据的矩阵
    all_models_data = [];
    
    % 循环处理每个模型
    for m = 1:length(models)
        model = models{m};
        
        % 构建模型输入目录
        model_input_dir = fullfile(input_dir, ssp, model);
        
        % 初始化存储所有年份数据的矩阵
        all_years_data = [];
        
        % 循环读取每一年的数据
        for y = 2021:2100
            % 构建输入文件路径
            input_file = fullfile(model_input_dir, sprintf('EPEs_%d.csv', y));
            
            % 读取CSV文件
            data = readtable(input_file);
            
            % 将数据转换为数组
            year_data = table2array(data);
            
            % 将年度数据添加到总矩阵中
            if isempty(all_years_data)
                all_years_data = zeros(size(year_data,1), size(year_data,2), 80); % 80年数据
            end
            all_years_data(:,:,y-2020) = year_data;
            
            fprintf('已读取%s_%s_%d年校正后的未来极端降水数据\n', model, ssp, y);
        end
        
        % 将当前模型的数据添加到所有模型数据中
        if isempty(all_models_data)
            all_models_data = zeros(size(all_years_data,1), size(all_years_data,2), size(all_years_data,3), length(models));
        end
        all_models_data(:,:,:,m) = all_years_data;
    end
    
    % 计算多模式平均值(MME)
    mme_data = squeeze(nanmean(all_models_data, 4));
    
    % 为MME创建输出目录
    output_dir = fullfile(input_dir, ssp, 'MME');
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    
    % 保存MME数据
    for y = 2021:2100
        year_data = mme_data(:,:,y-2020);
        % 创建表格
        T = array2table(year_data, 'VariableNames', HW_indexs);
        % 保存为CSV
        output_file = fullfile(output_dir, sprintf('EPEs_%d.csv', y));
        writetable(T, output_file);
        fprintf('已保存%s情景%d年MME数据\n', ssp, y);
    end
end

