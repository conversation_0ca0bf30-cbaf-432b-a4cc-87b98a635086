#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
校正后未来情景极端降水所有模式RX1-day增长率分析脚本
分析所有模式的RX1-day有效像元平均值，计算增长率，
然后计算所有模式增长率的平均值以及上下限（95和5分位数），最后绘图

作者: 自动生成
日期: 2025-08-26
"""

import os
import glob
import rasterio
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def setup_paths():
    """设置数据路径"""
    # 掩膜后的历史多年平均数据路径
    historical_path = r"Z:/yuan/paper3_new02/EPEs_clip/historical_mean/"
    
    # 校正后未来情景极端降水数据路径（需要掩膜处理）
    future_models_path = r"Z:/yuan/paper3_new02/EPEs/ssp_models_jz/"
    
    # 掩膜文件路径
    mask_file = r"Z:/yuan/clip_biaozhun_tif/clip_tif_1_basin4_600_1440.tif"
    
    return historical_path, future_models_path, mask_file

def load_mask(mask_file):
    """加载掩膜文件"""
    try:
        with rasterio.open(mask_file) as src:
            mask_data = src.read(1)
            logger.info(f"成功加载掩膜文件: {mask_file}")
            logger.info(f"掩膜文件形状: {mask_data.shape}")
            logger.info(f"有效像元数量: {np.sum(mask_data == 1)}")
            return mask_data
    except Exception as e:
        logger.error(f"加载掩膜文件失败: {e}")
        raise

def extract_valid_pixel_mean_with_mask(tif_file, mask_data):
    """使用掩膜提取有效像元的平均值"""
    try:
        with rasterio.open(tif_file) as src:
            data = src.read(1)
            nodata = src.nodata
            
            # 应用掩膜：只保留掩膜为1的像元
            masked_data = np.where(mask_data == 1, data, np.nan)
            
            # 进一步排除nodata值和异常值
            if nodata is not None:
                valid_mask = (masked_data != nodata) & (~np.isnan(masked_data)) & (masked_data > -9999) & (masked_data < 1e10)
            else:
                valid_mask = (~np.isnan(masked_data)) & (masked_data > -9999) & (masked_data < 1e10)
            
            valid_data = masked_data[valid_mask]
            
            if len(valid_data) > 0:
                mean_value = np.nanmean(valid_data)
                return mean_value
            else:
                logger.warning(f"文件 {tif_file} 没有有效像元")
                return np.nan
                
    except Exception as e:
        logger.error(f"处理文件 {tif_file} 失败: {e}")
        return np.nan

def get_historical_baseline(historical_path):
    """获取历史多年平均基准值"""
    rx1day_file = os.path.join(historical_path, "RX1-day_1971_2020_mean.tif")
    
    if not os.path.exists(rx1day_file):
        logger.error(f"历史RX1-day文件不存在: {rx1day_file}")
        return None
    
    # 对于历史数据，假设已经掩膜处理过，直接提取
    try:
        with rasterio.open(rx1day_file) as src:
            data = src.read(1)
            nodata = src.nodata
            
            if nodata is not None:
                valid_data = data[(data != nodata) & (~np.isnan(data)) & (data > -9999)]
            else:
                valid_data = data[(~np.isnan(data)) & (data > -9999)]
            
            if len(valid_data) > 0:
                baseline = np.nanmean(valid_data)
                logger.info(f"历史基准值 (1971-2020): {baseline:.4f}")
                return baseline
            else:
                logger.error("历史基准文件没有有效数据")
                return None
    except Exception as e:
        logger.error(f"读取历史基准文件失败: {e}")
        return None

def get_available_models(future_models_path, scenarios):
    """获取可用的模式列表"""
    all_models = set()
    
    for scenario in scenarios:
        scenario_path = os.path.join(future_models_path, scenario)
        if os.path.exists(scenario_path):
            models = [d for d in os.listdir(scenario_path) 
                     if os.path.isdir(os.path.join(scenario_path, d))]
            all_models.update(models)
    
    models_list = sorted(list(all_models))
    logger.info(f"发现 {len(models_list)} 个模式: {models_list}")
    return models_list

def extract_model_data(future_models_path, scenarios, models, years, mask_data):
    """提取所有模式的数据"""
    model_data = {}
    
    for scenario in scenarios:
        model_data[scenario] = {}
        logger.info(f"处理情景: {scenario}")
        
        for model in models:
            model_data[scenario][model] = {}
            model_path = os.path.join(future_models_path, scenario, model, "RX1-day")
            
            if not os.path.exists(model_path):
                logger.warning(f"模式路径不存在: {model_path}")
                continue
            
            logger.info(f"  处理模式: {model}")
            
            for year in years:
                rx1day_file = os.path.join(model_path, f"RX1-day_{year}.tif")
                
                if os.path.exists(rx1day_file):
                    mean_value = extract_valid_pixel_mean_with_mask(rx1day_file, mask_data)
                    model_data[scenario][model][year] = mean_value
                else:
                    model_data[scenario][model][year] = np.nan
    
    return model_data

def calculate_model_growth_rates(model_data, baseline, scenarios, models):
    """计算每个模式的增长率"""
    growth_rates = {}
    
    for scenario in scenarios:
        growth_rates[scenario] = {}
        
        for model in models:
            if model in model_data[scenario]:
                growth_rates[scenario][model] = {}
                
                for year, value in model_data[scenario][model].items():
                    if not np.isnan(value) and baseline is not None and not np.isnan(baseline) and baseline != 0:
                        growth_rate = (value - baseline) / baseline * 100
                        growth_rates[scenario][model][year] = growth_rate
                    else:
                        growth_rates[scenario][model][year] = np.nan
    
    return growth_rates

def calculate_ensemble_statistics(growth_rates, scenarios, years):
    """计算集合统计量（平均值、95分位数、5分位数）"""
    ensemble_stats = {}
    
    for scenario in scenarios:
        ensemble_stats[scenario] = {
            'mean': {},
            'p95': {},
            'p05': {}
        }
        
        for year in years:
            # 收集所有模式在该年份的增长率
            year_rates = []
            for model in growth_rates[scenario]:
                if year in growth_rates[scenario][model]:
                    rate = growth_rates[scenario][model][year]
                    if not np.isnan(rate):
                        year_rates.append(rate)
            
            if len(year_rates) > 0:
                ensemble_stats[scenario]['mean'][year] = np.mean(year_rates)
                ensemble_stats[scenario]['p95'][year] = np.percentile(year_rates, 95)
                ensemble_stats[scenario]['p05'][year] = np.percentile(year_rates, 5)
                logger.debug(f"{scenario} {year}: {len(year_rates)} 个有效模式")
            else:
                ensemble_stats[scenario]['mean'][year] = np.nan
                ensemble_stats[scenario]['p95'][year] = np.nan
                ensemble_stats[scenario]['p05'][year] = np.nan
    
    return ensemble_stats

def create_ensemble_plot(ensemble_stats, scenarios, output_path):
    """创建集合统计时间序列图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    years = list(range(2025, 2101, 5))  # 2025, 2030, ..., 2100
    colors = {'ssp126': '#1f77b4', 'ssp245': '#ff7f0e', 'ssp370': '#2ca02c', 'ssp585': '#d62728'}
    
    for i, scenario in enumerate(scenarios):
        ax = axes[i]
        
        if scenario in ensemble_stats:
            # 提取数据
            mean_values = [ensemble_stats[scenario]['mean'].get(year, np.nan) for year in years]
            p95_values = [ensemble_stats[scenario]['p95'].get(year, np.nan) for year in years]
            p05_values = [ensemble_stats[scenario]['p05'].get(year, np.nan) for year in years]
            
            # 转换为numpy数组
            mean_array = np.array(mean_values, dtype=float)
            p95_array = np.array(p95_values, dtype=float)
            p05_array = np.array(p05_values, dtype=float)
            
            # 找到有效数据点
            valid_mean_indices = ~np.isnan(mean_array)
            valid_interval_indices = ~(np.isnan(p95_array) | np.isnan(p05_array))
            
            if np.any(valid_mean_indices):
                valid_years_mean = np.array(years)[valid_mean_indices]
                valid_mean = mean_array[valid_mean_indices]
                
                # 绘制主线（平均值）
                ax.plot(valid_years_mean, valid_mean, color=colors[scenario], linewidth=2, 
                       label=f'{scenario.upper()} 集合平均', marker='o', markersize=4)
            
            # 绘制置信区间
            if np.any(valid_interval_indices):
                valid_years_interval = np.array(years)[valid_interval_indices]
                valid_p95_interval = p95_array[valid_interval_indices]
                valid_p05_interval = p05_array[valid_interval_indices]
                
                ax.fill_between(valid_years_interval, valid_p05_interval, valid_p95_interval, 
                               color=colors[scenario], alpha=0.3, 
                               label=f'{scenario.upper()} P05-P95')
                
                # 绘制上下边界线
                ax.plot(valid_years_interval, valid_p95_interval, color=colors[scenario], 
                       linestyle='--', alpha=0.7, linewidth=1)
                ax.plot(valid_years_interval, valid_p05_interval, color=colors[scenario], 
                       linestyle='--', alpha=0.7, linewidth=1)
        
        # 设置图形属性
        ax.set_title(f'{scenario.upper()} RX1-day增长率 (所有模式集合)', fontsize=14, fontweight='bold')
        ax.set_xlabel('年份', fontsize=12)
        ax.set_ylabel('增长率 (%)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 设置x轴
        ax.set_xlim(2020, 2105)
        ax.set_xticks(range(2025, 2101, 15))
        
        # 添加零线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=0.8)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    logger.info(f"集合统计时间序列图已保存到: {output_path}")

def main():
    """主函数"""
    logger.info("开始校正后未来情景所有模式RX1-day增长率分析...")
    
    # 设置路径
    historical_path, future_models_path, mask_file = setup_paths()
    
    # 定义参数
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    years = list(range(2025, 2101, 5))  # 2025, 2030, ..., 2100
    
    # 加载掩膜
    logger.info("加载掩膜文件...")
    mask_data = load_mask(mask_file)
    
    # 获取历史基准值
    logger.info("提取历史基准值...")
    baseline = get_historical_baseline(historical_path)
    
    if baseline is None:
        logger.error("无法获取历史基准值，程序终止")
        return
    
    # 获取可用模式
    logger.info("获取可用模式...")
    models = get_available_models(future_models_path, scenarios)
    
    if len(models) == 0:
        logger.error("没有找到可用的模式，程序终止")
        return
    
    # 提取所有模式数据
    logger.info("提取所有模式数据...")
    model_data = extract_model_data(future_models_path, scenarios, models, years, mask_data)
    
    # 计算每个模式的增长率
    logger.info("计算每个模式的增长率...")
    growth_rates = calculate_model_growth_rates(model_data, baseline, scenarios, models)
    
    # 计算集合统计量
    logger.info("计算集合统计量...")
    ensemble_stats = calculate_ensemble_statistics(growth_rates, scenarios, years)
    
    # 创建输出目录
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存详细结果
    logger.info("保存详细结果...")
    
    # 保存每个模式的增长率
    detailed_results = []
    for scenario in scenarios:
        for model in models:
            if scenario in growth_rates and model in growth_rates[scenario]:
                for year, rate in growth_rates[scenario][model].items():
                    detailed_results.append({
                        'Scenario': scenario,
                        'Model': model,
                        'Year': year,
                        'Growth_Rate_Percent': rate if not np.isnan(rate) else 'NaN'
                    })
    
    detailed_df = pd.DataFrame(detailed_results)
    detailed_csv = os.path.join(output_dir, "rx1day_all_models_growth_rates.csv")
    detailed_df.to_csv(detailed_csv, index=False, encoding='utf-8-sig')
    logger.info(f"详细结果已保存到: {detailed_csv}")
    
    # 保存集合统计结果
    ensemble_results = []
    for scenario in scenarios:
        for year in years:
            ensemble_results.append({
                'Scenario': scenario,
                'Year': year,
                'Ensemble_Mean': ensemble_stats[scenario]['mean'].get(year, np.nan),
                'P95': ensemble_stats[scenario]['p95'].get(year, np.nan),
                'P05': ensemble_stats[scenario]['p05'].get(year, np.nan)
            })
    
    ensemble_df = pd.DataFrame(ensemble_results)
    ensemble_csv = os.path.join(output_dir, "rx1day_ensemble_statistics.csv")
    ensemble_df.to_csv(ensemble_csv, index=False, encoding='utf-8-sig')
    logger.info(f"集合统计结果已保存到: {ensemble_csv}")
    
    # 创建集合统计时间序列图
    logger.info("绘制集合统计时间序列图...")
    plot_output = os.path.join(output_dir, "rx1day_ensemble_timeseries.png")
    create_ensemble_plot(ensemble_stats, scenarios, plot_output)
    
    logger.info("分析完成!")

if __name__ == "__main__":
    main()
