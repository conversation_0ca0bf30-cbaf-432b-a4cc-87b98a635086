#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RX1-day标准差统计结果美观绘图脚本
读取CSV数据，绘制带实心点的美观折线图

作者: 自动生成
日期: 2025-08-26
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置图形样式
plt.style.use('seaborn-v0_8-whitegrid')

# 设置中文字体和高质量参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.alpha'] = 0.25

def setup_paths():
    """设置输入输出路径"""
    input_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth7"
    csv_file = os.path.join(input_dir, "rx1day_ensemble_statistics_std.csv")
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth7"
    return csv_file, output_dir

def load_ensemble_data(csv_file):
    """加载集合统计数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        logger.info(f"成功加载数据: {csv_file}")
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"包含情景: {df['Scenario'].unique()}")
        logger.info(f"年份范围: {df['Year'].min()} - {df['Year'].max()}")
        return df
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise

def create_enhanced_polyline_plot(df, output_path):
    """创建增强版折线图（实心点）"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 9))
    
    # 使用指定的配色方案
    colors = {
        "ssp585": "#c3272b",  # 红色
        "ssp370": "#fa8c35",  # 橙色
        "ssp245": "#bce672",  # 浅绿色
        "ssp126": "#0B7751"   # 深绿色
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    # 按照从低到高的排放情景顺序绘制
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        scenario_data = df[df['Scenario'] == scenario].copy()
        scenario_data = scenario_data.sort_values('Year')
        
        if len(scenario_data) == 0:
            logger.warning(f"情景 {scenario} 没有数据")
            continue
        
        years = scenario_data['Year'].values
        mean_values = scenario_data['Ensemble_Mean'].values
        upper_values = scenario_data['Upper_Std'].values
        lower_values = scenario_data['Lower_Std'].values
        
        # 处理NaN值
        valid_indices = ~(np.isnan(mean_values) | np.isnan(upper_values) | np.isnan(lower_values))
        
        if np.any(valid_indices):
            valid_years = years[valid_indices]
            valid_mean = mean_values[valid_indices]
            valid_upper = upper_values[valid_indices]
            valid_lower = lower_values[valid_indices]
            
            # 绘制置信区间（平均值±标准差）
            ax.fill_between(valid_years, valid_lower, valid_upper, 
                           color=colors[scenario], alpha=0.15, zorder=1,
                           label=f'{scenario_labels[scenario]} 不确定性范围')
            
            # 绘制主线（更粗的线条）
            line = ax.plot(valid_years, valid_mean, color=colors[scenario], 
                          linewidth=4, alpha=0.9, zorder=5,
                          solid_capstyle='round', solid_joinstyle='round')
            
            # 绘制实心点（更大更美观）
            scatter = ax.scatter(valid_years, valid_mean, 
                               color=colors[scenario], 
                               s=120,  # 增大点的大小
                               alpha=1.0, 
                               zorder=6,
                               edgecolors='white', 
                               linewidth=2.5,
                               label=scenario_labels[scenario])
            
            # 添加数据点的微妙阴影效果
            ax.scatter(valid_years, valid_mean, 
                      color='black', 
                      s=125,  # 稍大一点作为阴影
                      alpha=0.1, 
                      zorder=4)
    
    # 优化图形外观
    ax.set_title('RX1-day极端降水增长率预估\n(集合平均值 ± 标准差)', 
                fontsize=22, fontweight='bold', 
                pad=35, color='#2C3E50', linespacing=1.2)
    
    ax.set_xlabel('年份', fontsize=18, fontweight='bold', color='#34495E', labelpad=15)
    ax.set_ylabel('增长率 (%)', fontsize=18, fontweight='bold', color='#34495E', labelpad=15)
    
    # 设置更精细的网格
    ax.grid(True, alpha=0.15, linestyle='-', linewidth=0.8, color='#BDC3C7')
    ax.set_facecolor('#FAFBFC')  # 更淡的背景色
    
    # 设置坐标轴范围和刻度
    ax.set_xlim(2020, 2105)
    y_max = max(df['Upper_Std'].max() * 1.1, 50)
    y_min = min(df['Lower_Std'].min() * 1.1, -10)
    ax.set_ylim(y_min, y_max)
    
    ax.set_xticks(range(2025, 2101, 15))
    ax.set_xticklabels(range(2025, 2101, 15), fontsize=14)
    ax.tick_params(axis='both', which='major', labelsize=14, colors='#2C3E50',
                   length=8, width=1.5, pad=8)
    
    # 零线（更突出）
    ax.axhline(y=0, color='#34495E', linestyle='-', alpha=0.9, linewidth=2.5, zorder=3)
    
    # 创建自定义图例
    # 只显示主要的情景线，不显示不确定性范围
    handles = []
    labels = []
    for scenario in scenario_order:
        if scenario in df['Scenario'].values:
            # 创建线条+点的组合图例
            line_handle = plt.Line2D([0], [0], color=colors[scenario], linewidth=4, 
                                   marker='o', markersize=10, markerfacecolor=colors[scenario],
                                   markeredgecolor='white', markeredgewidth=2)
            handles.append(line_handle)
            labels.append(scenario_labels[scenario])
    
    legend = ax.legend(handles, labels, loc='upper left', fontsize=16, 
                      frameon=True, fancybox=True, shadow=True, 
                      framealpha=0.95, edgecolor='#BDC3C7', 
                      facecolor='white', borderpad=1.2, 
                      handlelength=3, handletextpad=1)
    
    # 美化图例
    legend.get_frame().set_linewidth(1.5)
    
    # 移除顶部和右侧边框，美化其他边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#BDC3C7')
    ax.spines['bottom'].set_color('#BDC3C7')
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 添加细微的外边距
    plt.subplots_adjust(left=0.1, right=0.95, top=0.9, bottom=0.15)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none',
                pad_inches=0.2)
    plt.show()
    
    logger.info(f"增强版折线图已保存到: {output_path}")

def create_publication_style_plot(df, output_path):
    """创建发表级别的简洁图表"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 7))
    
    colors = {
        "ssp585": "#c3272b",
        "ssp370": "#fa8c35", 
        "ssp245": "#bce672",
        "ssp126": "#0B7751"
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        scenario_data = df[df['Scenario'] == scenario].copy()
        scenario_data = scenario_data.sort_values('Year')
        
        if len(scenario_data) == 0:
            continue
        
        years = scenario_data['Year'].values
        mean_values = scenario_data['Ensemble_Mean'].values
        upper_values = scenario_data['Upper_Std'].values
        lower_values = scenario_data['Lower_Std'].values
        
        valid_indices = ~(np.isnan(mean_values) | np.isnan(upper_values) | np.isnan(lower_values))
        
        if np.any(valid_indices):
            valid_years = years[valid_indices]
            valid_mean = mean_values[valid_indices]
            valid_upper = upper_values[valid_indices]
            valid_lower = lower_values[valid_indices]
            
            # 置信区间
            ax.fill_between(valid_years, valid_lower, valid_upper, 
                           color=colors[scenario], alpha=0.2, zorder=1)
            
            # 主线
            ax.plot(valid_years, valid_mean, color=colors[scenario], 
                   linewidth=3, alpha=0.95, zorder=5)
            
            # 实心点
            ax.scatter(valid_years, valid_mean, 
                      color=colors[scenario], 
                      s=80, alpha=1.0, zorder=6,
                      edgecolors='white', linewidth=2,
                      label=scenario_labels[scenario])
    
    # 发表级别的设置
    ax.set_title('RX1-day Growth Rate Projections (Mean ± Std)', 
                fontsize=16, fontweight='bold', pad=20, color='black')
    ax.set_xlabel('Year', fontsize=14, color='black')
    ax.set_ylabel('Growth Rate (%)', fontsize=14, color='black')
    
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-15, 50)
    ax.set_xticks(range(2030, 2101, 20))
    ax.tick_params(axis='both', which='major', labelsize=12, colors='black')
    
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.6, linewidth=1.5)
    
    legend = ax.legend(loc='upper left', fontsize=12, frameon=True, 
                      framealpha=0.9, edgecolor='gray')
    
    for spine in ax.spines.values():
        spine.set_color('black')
        spine.set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    logger.info(f"发表版本已保存到: {output_path}")

def create_summary_statistics(df, output_dir):
    """创建统计摘要"""
    summary_stats = []
    
    scenarios = df['Scenario'].unique()
    for scenario in scenarios:
        scenario_data = df[df['Scenario'] == scenario]
        
        # 计算2050年和2100年的统计
        for target_year in [2050, 2100]:
            year_data = scenario_data[scenario_data['Year'] == target_year]
            if len(year_data) > 0:
                mean_val = year_data['Ensemble_Mean'].iloc[0]
                std_val = year_data['Upper_Std'].iloc[0] - mean_val  # 标准差
                
                summary_stats.append({
                    'Scenario': scenario,
                    'Year': target_year,
                    'Mean_Growth_Rate': mean_val,
                    'Standard_Deviation': std_val,
                    'Upper_Bound': year_data['Upper_Std'].iloc[0],
                    'Lower_Bound': year_data['Lower_Std'].iloc[0]
                })
    
    summary_df = pd.DataFrame(summary_stats)
    summary_file = os.path.join(output_dir, "rx1day_summary_statistics.csv")
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    logger.info(f"统计摘要已保存到: {summary_file}")
    
    return summary_df

def main():
    """主函数"""
    logger.info("开始创建RX1-day标准差统计结果美观绘图...")
    
    # 设置路径
    csv_file, output_dir = setup_paths()
    
    # 检查输入文件
    if not os.path.exists(csv_file):
        logger.error(f"输入CSV文件不存在: {csv_file}")
        return
    
    # 加载数据
    df = load_ensemble_data(csv_file)
    
    # 创建增强版折线图
    logger.info("创建增强版折线图（实心点）...")
    enhanced_output = os.path.join(output_dir, "rx1day_std_enhanced.png")
    create_enhanced_polyline_plot(df, enhanced_output)
    
    # 创建发表版本
    logger.info("创建发表版本...")
    publication_output = os.path.join(output_dir, "rx1day_std_publication.png")
    create_publication_style_plot(df, publication_output)
    
    # 创建统计摘要
    logger.info("创建统计摘要...")
    summary_df = create_summary_statistics(df, output_dir)
    
    logger.info("美观绘图完成!")
    logger.info(f"输出文件:")
    logger.info(f"  - 增强版图表: {enhanced_output}")
    logger.info(f"  - 发表版图表: {publication_output}")
    logger.info(f"  - 统计摘要: {os.path.join(output_dir, 'rx1day_summary_statistics.csv')}")

if __name__ == "__main__":
    main()
