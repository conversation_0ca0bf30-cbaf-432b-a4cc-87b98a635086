#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RX1-day集合统计结果最终优化绘图脚本
使用指定配色方案，调整曲线平滑度，提供多种绘图版本

作者: 自动生成
日期: 2025-08-26
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import make_interp_spline, interp1d
import seaborn as sns
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置图形样式
plt.style.use('seaborn-v0_8-whitegrid')

# 设置中文字体和高质量参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.alpha'] = 0.25

def setup_paths():
    """设置输入输出路径"""
    input_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth2"
    ensemble_csv = os.path.join(input_dir, "rx1day_ensemble_statistics.csv")
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth6"
    return ensemble_csv, output_dir

def load_ensemble_data(csv_file):
    """加载集合统计数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        logger.info(f"成功加载数据: {csv_file}")
        scenarios = df['Scenario'].unique()
        years = sorted(df['Year'].unique())
        return df, scenarios, years
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise

def smooth_curve_moderate(x, y, smoothness='moderate'):
    """创建适度平滑的曲线"""
    if len(x) < 3:
        return x, y
    
    try:
        if smoothness == 'moderate':
            # 使用线性插值创建适度平滑
            f = interp1d(x, y, kind='cubic')
            x_smooth = np.linspace(x.min(), x.max(), len(x) * 3)
            y_smooth = f(x_smooth)
        elif smoothness == 'light':
            # 轻度平滑
            f = interp1d(x, y, kind='quadratic')
            x_smooth = np.linspace(x.min(), x.max(), len(x) * 2)
            y_smooth = f(x_smooth)
        else:
            # 原始数据
            return x, y
            
        return x_smooth, y_smooth
    except:
        return x, y

def create_smooth_version(df, scenarios, years, output_path):
    """创建适度平滑版本"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 使用指定的配色方案
    colors = {
        "ssp585": "#c3272b",  # 红色
        "ssp370": "#fa8c35",  # 橙色
        "ssp245": "#bce672",  # 绿色
        "ssp126": "#0B7751"   # 深绿色
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    # 按照从低到高的排放情景顺序绘制
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        if scenario in scenarios and scenario in colors:
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 创建适度平滑曲线
                years_smooth, mean_smooth = smooth_curve_moderate(valid_years, valid_mean, 'moderate')
                years_smooth_p95, p95_smooth = smooth_curve_moderate(valid_years, valid_p95, 'moderate')
                years_smooth_p05, p05_smooth = smooth_curve_moderate(valid_years, valid_p05, 'moderate')
                
                # 绘制置信区间
                ax.fill_between(years_smooth_p95, p05_smooth, p95_smooth, 
                               color=colors[scenario], alpha=0.12, zorder=1)
                
                # 绘制平滑主线
                ax.plot(years_smooth, mean_smooth, color=colors[scenario], 
                       linewidth=3.5, label=scenario_labels[scenario], 
                       alpha=0.9, zorder=5)
                
                # 绘制数据点
                ax.scatter(valid_years, valid_mean, color=colors[scenario], 
                          s=80, alpha=0.9, zorder=6, edgecolors='white', 
                          linewidth=2)
    
    # 优化图形外观
    ax.set_title('RX1-day极端降水增长率预估', fontsize=20, fontweight='bold', 
                pad=30, color='#2C3E50')
    ax.set_xlabel('年份', fontsize=16, fontweight='bold', color='#34495E')
    ax.set_ylabel('增长率 (%)', fontsize=16, fontweight='bold', color='#34495E')
    
    # 设置网格和背景
    ax.grid(True, alpha=0.2, linestyle='-', linewidth=0.8, color='#BDC3C7')
    ax.set_facecolor('#FAFAFA')
    
    # 设置坐标轴
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-2, max(df['P95'].max() * 1.05, 35))
    ax.set_xticks(range(2025, 2101, 15))
    ax.tick_params(axis='both', which='major', labelsize=13, colors='#2C3E50')
    
    # 零线
    ax.axhline(y=0, color='#34495E', linestyle='-', alpha=0.8, linewidth=2)
    
    # 优化图例
    legend = ax.legend(loc='upper left', fontsize=14, frameon=True, 
                      fancybox=True, shadow=True, framealpha=0.95, 
                      edgecolor='#BDC3C7', facecolor='white')
    
    # 移除边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#BDC3C7')
    ax.spines['bottom'].set_color('#BDC3C7')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    logger.info(f"适度平滑版本已保存到: {output_path}")

def create_polyline_version(df, scenarios, years, output_path):
    """创建折线版本"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    colors = {
        "ssp585": "#c3272b",
        "ssp370": "#fa8c35",
        "ssp245": "#bce672",
        "ssp126": "#0B7751"
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        if scenario in scenarios and scenario in colors:
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 绘制置信区间
                ax.fill_between(valid_years, valid_p05, valid_p95, 
                               color=colors[scenario], alpha=0.12, zorder=1)
                
                # 绘制折线（不平滑）
                ax.plot(valid_years, valid_mean, color=colors[scenario], 
                       linewidth=3, label=scenario_labels[scenario], 
                       alpha=0.9, zorder=5, marker='o', markersize=8,
                       markerfacecolor='white', markeredgecolor=colors[scenario],
                       markeredgewidth=2)
    
    # 图形设置与平滑版本相同
    ax.set_title('RX1-day极端降水增长率预估 (折线版)', fontsize=20, fontweight='bold', 
                pad=30, color='#2C3E50')
    ax.set_xlabel('年份', fontsize=16, fontweight='bold', color='#34495E')
    ax.set_ylabel('增长率 (%)', fontsize=16, fontweight='bold', color='#34495E')
    
    ax.grid(True, alpha=0.2, linestyle='-', linewidth=0.8, color='#BDC3C7')
    ax.set_facecolor('#FAFAFA')
    
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-2, max(df['P95'].max() * 1.05, 35))
    ax.set_xticks(range(2025, 2101, 15))
    ax.tick_params(axis='both', which='major', labelsize=13, colors='#2C3E50')
    
    ax.axhline(y=0, color='#34495E', linestyle='-', alpha=0.8, linewidth=2)
    
    legend = ax.legend(loc='upper left', fontsize=14, frameon=True, 
                      fancybox=True, shadow=True, framealpha=0.95, 
                      edgecolor='#BDC3C7', facecolor='white')
    
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#BDC3C7')
    ax.spines['bottom'].set_color('#BDC3C7')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    logger.info(f"折线版本已保存到: {output_path}")

def create_publication_version(df, scenarios, years, output_path):
    """创建发表级别的图表"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 7))
    
    colors = {
        "ssp585": "#c3272b",
        "ssp370": "#fa8c35",
        "ssp245": "#bce672",
        "ssp126": "#0B7751"
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        if scenario in scenarios and scenario in colors:
            scenario_data = df[df['Scenario'] == scenario].copy()
            scenario_data = scenario_data.sort_values('Year')
            
            years_data = scenario_data['Year'].values
            mean_values = scenario_data['Ensemble_Mean'].values
            p95_values = scenario_data['P95'].values
            p05_values = scenario_data['P05'].values
            
            valid_indices = ~(np.isnan(mean_values) | np.isnan(p95_values) | np.isnan(p05_values))
            
            if np.any(valid_indices):
                valid_years = years_data[valid_indices]
                valid_mean = mean_values[valid_indices]
                valid_p95 = p95_values[valid_indices]
                valid_p05 = p05_values[valid_indices]
                
                # 轻度平滑
                years_smooth, mean_smooth = smooth_curve_moderate(valid_years, valid_mean, 'light')
                years_smooth_p95, p95_smooth = smooth_curve_moderate(valid_years, valid_p95, 'light')
                years_smooth_p05, p05_smooth = smooth_curve_moderate(valid_years, valid_p05, 'light')
                
                # 置信区间
                ax.fill_between(years_smooth_p95, p05_smooth, p95_smooth, 
                               color=colors[scenario], alpha=0.15, zorder=1)
                
                # 主线
                ax.plot(years_smooth, mean_smooth, color=colors[scenario], 
                       linewidth=2.5, label=scenario_labels[scenario], 
                       alpha=0.95, zorder=5)
    
    # 发表级别的设置
    ax.set_title('RX1-day Growth Rate Projections', fontsize=16, fontweight='bold', 
                pad=20, color='black')
    ax.set_xlabel('Year', fontsize=14, color='black')
    ax.set_ylabel('Growth Rate (%)', fontsize=14, color='black')
    
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-2, 35)
    ax.set_xticks(range(2030, 2101, 20))
    ax.tick_params(axis='both', which='major', labelsize=12, colors='black')
    
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.6, linewidth=1)
    
    legend = ax.legend(loc='upper left', fontsize=12, frameon=True, 
                      framealpha=0.9, edgecolor='gray')
    
    for spine in ax.spines.values():
        spine.set_color('black')
        spine.set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    logger.info(f"发表版本已保存到: {output_path}")

def main():
    """主函数"""
    logger.info("开始创建最终优化版RX1-day时间序列图...")
    
    # 设置路径
    ensemble_csv, output_dir = setup_paths()
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查输入文件
    if not os.path.exists(ensemble_csv):
        logger.error(f"输入文件不存在: {ensemble_csv}")
        return
    
    # 加载数据
    df, scenarios, years = load_ensemble_data(ensemble_csv)
    
    # 创建适度平滑版本
    logger.info("创建适度平滑版本...")
    smooth_output = os.path.join(output_dir, "rx1day_smooth_final.png")
    create_smooth_version(df, scenarios, years, smooth_output)
    
    # 创建折线版本
    logger.info("创建折线版本...")
    polyline_output = os.path.join(output_dir, "rx1day_polyline_final.png")
    create_polyline_version(df, scenarios, years, polyline_output)
    
    # 创建发表版本
    logger.info("创建发表版本...")
    publication_output = os.path.join(output_dir, "rx1day_publication_final.png")
    create_publication_version(df, scenarios, years, publication_output)
    
    logger.info("最终优化绘图完成!")

if __name__ == "__main__":
    main()
