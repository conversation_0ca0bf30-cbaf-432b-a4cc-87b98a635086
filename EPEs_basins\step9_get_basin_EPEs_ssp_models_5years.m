clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 定义SSP情景和年份范围
ssps = {'ssp126', 'ssp245', 'ssp370', 'ssp585'};
models = {'MME','ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};

% 设置输入和输出目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs_corrected';
output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/ssp/basins_EPEs_corrected_5years2';

% 读取1971年的流域ID
basin_file = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_daily_mean/basin_daily_means_1971.csv';
basin_data = readtable(basin_file);
basin_ids = basin_data.BasinID;

% 循环处理每个SSP情景
for s = 1:length(ssps)
    ssp = ssps{s};
    
    % 初始化存储所有模型数据的矩阵
    all_models_data = [];
    
    % 循环处理每个模型
    for m = 1:length(models)
        model = models{m};
        
        % 构建模型输入目录
        model_input_dir = fullfile(input_dir, ssp, model);
        
        % 初始化存储所有年份数据的矩阵
        all_years_data = [];
        
        % 循环读取每一年的数据
        for y = 2021:2100
            % 构建输入文件路径
            input_file = fullfile(model_input_dir, sprintf('EPEs_%d.csv', y));
            
            % 读取CSV文件
            data = readtable(input_file);
            
            % 将数据转换为数组
            year_data = table2array(data);
            
            % 将年度数据添加到总矩阵中
            if isempty(all_years_data)
                all_years_data = zeros(size(year_data,1), size(year_data,2), 80); % 80年数据
            end
            all_years_data(:,:,y-2020) = year_data;
            
            fprintf('已读取%s_%s_%d年校正后的未来极端降水数据\n', model, ssp, y);
        end
        
        % 计算5年平均值
        for i = 1:16  % 16个5年段(2021-2025, 2026-2030, ..., 2096-2100)
            start_idx = (i-1)*5 + 1;
            end_idx = i*5;
            end_year = 2020 + end_idx;
            
            % 计算5年平均值
            five_year_mean = mean(all_years_data(:,:,start_idx:end_idx), 3, 'omitnan');
            
            % 创建输出目录
            model_output_dir = fullfile(output_dir, ssp, model);
            if ~exist(model_output_dir, 'dir')
                mkdir(model_output_dir);
            end
            
            % 将basin_ids添加到five_year_mean的第一列
            data_with_ids = [basin_ids, five_year_mean];
            
            % 创建列名(包含BasinID)
            column_names = ['BasinID', HW_indexs];
            
            % 保存5年平均值
            T = array2table(data_with_ids, 'VariableNames', column_names);
            output_file = fullfile(model_output_dir, sprintf('EPEs_%d.csv', end_year));
            writetable(T, output_file);
            
            fprintf('已保存%s_%s_%d年5年平均值数据\n', model, ssp, end_year);
        end
    end
end