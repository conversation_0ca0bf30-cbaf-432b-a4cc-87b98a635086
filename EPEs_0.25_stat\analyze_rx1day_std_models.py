#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
校正后未来情景极端降水所有模式RX1-day增长率分析脚本（标准差版本）
分析所有模式的RX1-day有效像元平均值，计算增长率，
然后计算所有模式增长率的平均值以及上下限（平均值±标准差），最后绘图

作者: 自动生成
日期: 2025-08-26
"""

import os
import glob
import rasterio
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.alpha'] = 0.25

def setup_paths():
    """设置数据路径"""
    # 掩膜后的历史多年平均数据路径
    historical_path = r"Z:/yuan/paper3_new02/EPEs_clip/historical_mean/"
    
    # 校正后未来情景极端降水数据路径（需要掩膜处理）
    future_models_path = r"Z:/yuan/paper3_new02/EPEs/ssp_models_jz/"
    
    # 掩膜文件路径
    mask_file = r"Z:/yuan/clip_biaozhun_tif/clip_tif_1_basin4_600_1440.tif"
    
    return historical_path, future_models_path, mask_file

def load_mask(mask_file):
    """加载掩膜文件"""
    try:
        with rasterio.open(mask_file) as src:
            mask_data = src.read(1)
            logger.info(f"成功加载掩膜文件: {mask_file}")
            logger.info(f"掩膜文件形状: {mask_data.shape}")
            logger.info(f"有效像元数量: {np.sum(mask_data == 1)}")
            return mask_data
    except Exception as e:
        logger.error(f"加载掩膜文件失败: {e}")
        raise

def extract_valid_pixel_mean_with_mask(tif_file, mask_data):
    """使用掩膜提取有效像元的平均值"""
    try:
        with rasterio.open(tif_file) as src:
            data = src.read(1)
            nodata = src.nodata
            
            # 应用掩膜：只保留掩膜为1的像元
            masked_data = np.where(mask_data == 1, data, np.nan)
            
            # 进一步排除nodata值和异常值
            if nodata is not None:
                valid_mask = (masked_data != nodata) & (~np.isnan(masked_data)) & (masked_data > -9999) & (masked_data < 1e10)
            else:
                valid_mask = (~np.isnan(masked_data)) & (masked_data > -9999) & (masked_data < 1e10)
            
            valid_data = masked_data[valid_mask]
            
            if len(valid_data) > 0:
                mean_value = np.nanmean(valid_data)
                return mean_value
            else:
                logger.warning(f"文件 {tif_file} 没有有效像元")
                return np.nan
                
    except Exception as e:
        logger.error(f"处理文件 {tif_file} 失败: {e}")
        return np.nan

def get_historical_baseline(historical_path):
    """获取历史多年平均基准值"""
    rx1day_file = os.path.join(historical_path, "RX1-day_1971_2020_mean.tif")
    
    if not os.path.exists(rx1day_file):
        logger.error(f"历史RX1-day文件不存在: {rx1day_file}")
        return None
    
    # 对于历史数据，假设已经掩膜处理过，直接提取
    try:
        with rasterio.open(rx1day_file) as src:
            data = src.read(1)
            nodata = src.nodata
            
            if nodata is not None:
                valid_data = data[(data != nodata) & (~np.isnan(data)) & (data > -9999)]
            else:
                valid_data = data[(~np.isnan(data)) & (data > -9999)]
            
            if len(valid_data) > 0:
                baseline = np.nanmean(valid_data)
                logger.info(f"历史基准值 (1971-2020): {baseline:.4f}")
                return baseline
            else:
                logger.error("历史基准文件没有有效数据")
                return None
    except Exception as e:
        logger.error(f"读取历史基准文件失败: {e}")
        return None

def get_available_models(future_models_path, scenarios):
    """获取可用的模式列表"""
    all_models = set()
    
    for scenario in scenarios:
        scenario_path = os.path.join(future_models_path, scenario)
        if os.path.exists(scenario_path):
            models = [d for d in os.listdir(scenario_path) 
                     if os.path.isdir(os.path.join(scenario_path, d))]
            all_models.update(models)
    
    models_list = sorted(list(all_models))
    logger.info(f"发现 {len(models_list)} 个模式: {models_list}")
    return models_list

def extract_model_data(future_models_path, scenarios, models, years, mask_data):
    """提取所有模式的数据"""
    model_data = {}
    
    for scenario in scenarios:
        model_data[scenario] = {}
        logger.info(f"处理情景: {scenario}")
        
        for model in models:
            model_data[scenario][model] = {}
            model_path = os.path.join(future_models_path, scenario, model, "RX1-day")
            
            if not os.path.exists(model_path):
                logger.warning(f"模式路径不存在: {model_path}")
                continue
            
            logger.info(f"  处理模式: {model}")
            
            for year in years:
                rx1day_file = os.path.join(model_path, f"RX1-day_{year}.tif")
                
                if os.path.exists(rx1day_file):
                    mean_value = extract_valid_pixel_mean_with_mask(rx1day_file, mask_data)
                    model_data[scenario][model][year] = mean_value
                else:
                    model_data[scenario][model][year] = np.nan
    
    return model_data

def calculate_model_growth_rates(model_data, baseline, scenarios, models):
    """计算每个模式的增长率"""
    growth_rates = {}
    
    for scenario in scenarios:
        growth_rates[scenario] = {}
        
        for model in models:
            if model in model_data[scenario]:
                growth_rates[scenario][model] = {}
                
                for year, value in model_data[scenario][model].items():
                    if not np.isnan(value) and baseline is not None and not np.isnan(baseline) and baseline != 0:
                        growth_rate = (value - baseline) / baseline * 100
                        growth_rates[scenario][model][year] = growth_rate
                    else:
                        growth_rates[scenario][model][year] = np.nan
    
    return growth_rates

def calculate_ensemble_statistics_std(growth_rates, scenarios, years):
    """计算集合统计量（平均值、平均值±标准差）"""
    ensemble_stats = {}
    
    for scenario in scenarios:
        ensemble_stats[scenario] = {
            'mean': {},
            'upper': {},  # 平均值 + 标准差
            'lower': {}   # 平均值 - 标准差
        }
        
        for year in years:
            # 收集所有模式在该年份的增长率
            year_rates = []
            for model in growth_rates[scenario]:
                if year in growth_rates[scenario][model]:
                    rate = growth_rates[scenario][model][year]
                    if not np.isnan(rate):
                        year_rates.append(rate)
            
            if len(year_rates) > 0:
                mean_rate = np.mean(year_rates)
                std_rate = np.std(year_rates)
                
                ensemble_stats[scenario]['mean'][year] = mean_rate
                ensemble_stats[scenario]['upper'][year] = mean_rate + std_rate
                ensemble_stats[scenario]['lower'][year] = mean_rate - std_rate
                
                logger.debug(f"{scenario} {year}: {len(year_rates)} 个有效模式, 平均值={mean_rate:.2f}%, 标准差={std_rate:.2f}%")
            else:
                ensemble_stats[scenario]['mean'][year] = np.nan
                ensemble_stats[scenario]['upper'][year] = np.nan
                ensemble_stats[scenario]['lower'][year] = np.nan
    
    return ensemble_stats

def create_polyline_plot_std(ensemble_stats, scenarios, years, output_path):
    """创建折线版本的标准差图表"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 使用指定的配色方案
    colors = {
        "ssp585": "#c3272b",
        "ssp370": "#fa8c35", 
        "ssp245": "#bce672",
        "ssp126": "#0B7751"
    }
    
    scenario_labels = {
        'ssp126': 'SSP1-2.6',
        'ssp245': 'SSP2-4.5', 
        'ssp370': 'SSP3-7.0',
        'ssp585': 'SSP5-8.5'
    }
    
    scenario_order = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    for scenario in scenario_order:
        if scenario in scenarios and scenario in colors:
            # 提取数据
            mean_values = [ensemble_stats[scenario]['mean'].get(year, np.nan) for year in years]
            upper_values = [ensemble_stats[scenario]['upper'].get(year, np.nan) for year in years]
            lower_values = [ensemble_stats[scenario]['lower'].get(year, np.nan) for year in years]
            
            # 处理NaN值
            valid_indices = ~(np.isnan(mean_values) | np.isnan(upper_values) | np.isnan(lower_values))
            
            if np.any(valid_indices):
                valid_years = np.array(years)[valid_indices]
                valid_mean = np.array(mean_values)[valid_indices]
                valid_upper = np.array(upper_values)[valid_indices]
                valid_lower = np.array(lower_values)[valid_indices]
                
                # 绘制置信区间（平均值±标准差）
                ax.fill_between(valid_years, valid_lower, valid_upper, 
                               color=colors[scenario], alpha=0.12, zorder=1)
                
                # 绘制折线（不平滑）
                ax.plot(valid_years, valid_mean, color=colors[scenario], 
                       linewidth=3, label=scenario_labels[scenario], 
                       alpha=0.9, zorder=5, marker='o', markersize=8,
                       markerfacecolor='white', markeredgecolor=colors[scenario],
                       markeredgewidth=2)
    
    # 图形设置
    ax.set_title('RX1-day极端降水增长率预估 (平均值±标准差)', fontsize=20, fontweight='bold', 
                pad=30, color='#2C3E50')
    ax.set_xlabel('年份', fontsize=16, fontweight='bold', color='#34495E')
    ax.set_ylabel('增长率 (%)', fontsize=16, fontweight='bold', color='#34495E')
    
    ax.grid(True, alpha=0.2, linestyle='-', linewidth=0.8, color='#BDC3C7')
    ax.set_facecolor('#FAFAFA')
    
    ax.set_xlim(2020, 2105)
    ax.set_ylim(-10, 50)  # 调整y轴范围以适应标准差
    ax.set_xticks(range(2025, 2101, 15))
    ax.tick_params(axis='both', which='major', labelsize=13, colors='#2C3E50')
    
    ax.axhline(y=0, color='#34495E', linestyle='-', alpha=0.8, linewidth=2)
    
    legend = ax.legend(loc='upper left', fontsize=14, frameon=True, 
                      fancybox=True, shadow=True, framealpha=0.95, 
                      edgecolor='#BDC3C7', facecolor='white')
    
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#BDC3C7')
    ax.spines['bottom'].set_color('#BDC3C7')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    logger.info(f"标准差版本折线图已保存到: {output_path}")

def main():
    """主函数"""
    logger.info("开始校正后未来情景所有模式RX1-day增长率分析（标准差版本）...")
    
    # 设置路径
    historical_path, future_models_path, mask_file = setup_paths()
    
    # 定义参数
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    years = list(range(2025, 2101, 5))  # 2025, 2030, ..., 2100
    
    # 加载掩膜
    logger.info("加载掩膜文件...")
    mask_data = load_mask(mask_file)
    
    # 获取历史基准值
    logger.info("提取历史基准值...")
    baseline = get_historical_baseline(historical_path)
    
    if baseline is None:
        logger.error("无法获取历史基准值，程序终止")
        return
    
    # 获取可用模式
    logger.info("获取可用模式...")
    models = get_available_models(future_models_path, scenarios)
    
    if len(models) == 0:
        logger.error("没有找到可用的模式，程序终止")
        return
    
    # 提取所有模式数据
    logger.info("提取所有模式数据...")
    model_data = extract_model_data(future_models_path, scenarios, models, years, mask_data)
    
    # 计算每个模式的增长率
    logger.info("计算每个模式的增长率...")
    growth_rates = calculate_model_growth_rates(model_data, baseline, scenarios, models)
    
    # 计算集合统计量（平均值±标准差）
    logger.info("计算集合统计量（平均值±标准差）...")
    ensemble_stats = calculate_ensemble_statistics_std(growth_rates, scenarios, years)
    
    # 创建输出目录
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth7"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存集合统计结果
    logger.info("保存集合统计结果...")
    ensemble_results = []
    for scenario in scenarios:
        for year in years:
            ensemble_results.append({
                'Scenario': scenario,
                'Year': year,
                'Ensemble_Mean': ensemble_stats[scenario]['mean'].get(year, np.nan),
                'Upper_Std': ensemble_stats[scenario]['upper'].get(year, np.nan),
                'Lower_Std': ensemble_stats[scenario]['lower'].get(year, np.nan)
            })
    
    ensemble_df = pd.DataFrame(ensemble_results)
    ensemble_csv = os.path.join(output_dir, "rx1day_ensemble_statistics_std.csv")
    ensemble_df.to_csv(ensemble_csv, index=False, encoding='utf-8-sig')
    logger.info(f"集合统计结果已保存到: {ensemble_csv}")
    
    # 创建折线版本图表
    logger.info("绘制标准差版本折线图...")
    plot_output = os.path.join(output_dir, "rx1day_std_polyline.png")
    create_polyline_plot_std(ensemble_stats, scenarios, years, plot_output)
    
    logger.info("分析完成!")

if __name__ == "__main__":
    main()
