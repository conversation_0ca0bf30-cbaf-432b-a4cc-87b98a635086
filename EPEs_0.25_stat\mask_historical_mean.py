#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史多年平均数据掩膜处理脚本
使用研究区掩膜文件对历史多年平均 (1971-2020年)数据进行掩膜处理
只保留有效像元部分，保存到指定目录

作者: 自动生成
日期: 2025-08-26
"""

import os
import glob
import rasterio
import numpy as np
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_paths():
    """设置输入输出路径"""
    # 掩膜文件路径
    mask_file = r"Z:/yuan/clip_biaozhun_tif/clip_tif_1_basin4_600_1440.tif"
    
    # 输入数据路径 - 历史多年平均数据
    input_base_path = r"Z:/yuan/paper3_new02/EPEs/historical_mean/"
    
    # 输出路径 - 掩膜后数据
    output_base_path = r"Z:/yuan/paper3_new02/EPEs_clip/historical_mean/"
    
    return mask_file, input_base_path, output_base_path

def load_mask(mask_file):
    """加载掩膜文件"""
    try:
        with rasterio.open(mask_file) as src:
            mask_data = src.read(1)
            mask_profile = src.profile
            logger.info(f"成功加载掩膜文件: {mask_file}")
            logger.info(f"掩膜文件形状: {mask_data.shape}")
            logger.info(f"有效像元数量: {np.sum(mask_data == 1)}")
            return mask_data, mask_profile
    except Exception as e:
        logger.error(f"加载掩膜文件失败: {e}")
        raise

def apply_mask(input_file, mask_data, output_file):
    """对单个文件应用掩膜"""
    try:
        with rasterio.open(input_file) as src:
            data = src.read(1)
            profile = src.profile
            
            # 应用掩膜：有效像元为1的保留，其他设为nodata
            masked_data = np.where(mask_data == 1, data, profile.get('nodata', -9999))
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # 保存掩膜后的数据
            with rasterio.open(output_file, 'w', **profile) as dst:
                dst.write(masked_data, 1)
                
            logger.info(f"成功处理: {os.path.basename(input_file)}")
            return True
            
    except Exception as e:
        logger.error(f"处理文件 {input_file} 失败: {e}")
        return False

def process_historical_mean(input_dir, output_dir, mask_data):
    """处理历史多年平均数据"""
    success_count = 0
    error_count = 0
    
    # 查找所有tif文件
    tif_pattern = os.path.join(input_dir, "*.tif")
    tif_files = glob.glob(tif_pattern)
    
    logger.info(f"在 {input_dir} 中找到 {len(tif_files)} 个tif文件")
    
    for input_file in tif_files:
        # 获取文件名
        filename = os.path.basename(input_file)
        output_file = os.path.join(output_dir, filename)
        
        # 应用掩膜
        if apply_mask(input_file, mask_data, output_file):
            success_count += 1
        else:
            error_count += 1
    
    return success_count, error_count

def main():
    """主函数"""
    logger.info("开始历史多年平均数据掩膜处理...")
    
    # 设置路径
    mask_file, input_base_path, output_base_path = setup_paths()
    
    # 检查输入路径是否存在
    if not os.path.exists(input_base_path):
        logger.error(f"输入路径不存在: {input_base_path}")
        return
    
    if not os.path.exists(mask_file):
        logger.error(f"掩膜文件不存在: {mask_file}")
        return
    
    # 加载掩膜文件
    mask_data, mask_profile = load_mask(mask_file)
    
    # 创建输出目录
    os.makedirs(output_base_path, exist_ok=True)
    
    # 处理历史多年平均数据
    logger.info("处理历史多年平均数据...")
    success, error = process_historical_mean(input_base_path, output_base_path, mask_data)
    
    # 输出处理结果
    logger.info("=" * 50)
    logger.info("处理完成!")
    logger.info(f"成功处理文件数: {success}")
    logger.info(f"处理失败文件数: {error}")
    logger.info(f"输出路径: {output_base_path}")
    logger.info("=" * 50)
    
    # 列出处理的文件
    if success > 0:
        logger.info("已处理的文件:")
        output_files = glob.glob(os.path.join(output_base_path, "*.tif"))
        for file in output_files:
            logger.info(f"  - {os.path.basename(file)}")

if __name__ == "__main__":
    main()
