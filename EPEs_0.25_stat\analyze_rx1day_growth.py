#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RX1-day增长率分析脚本
提取未来逐五年平均(2025-2100年)和历史多年平均(1971-2020年)RX1-day有效像元的平均值
计算增长率并绘制带上下区间的时间序列折线图

作者: 自动生成
日期: 2025-08-26
"""

import os
import glob
import rasterio
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def setup_paths():
    """设置数据路径"""
    # 掩膜后的历史多年平均数据路径
    historical_path = r"Z:/yuan/paper3_new02/EPEs_clip/historical_mean/"
    
    # 掩膜后的未来逐五年平均数据路径
    future_base_path = r"Z:/yuan/paper3_new02/EPEs_clip/ssp_mme_jz_5yr/"
    
    return historical_path, future_base_path

def extract_valid_pixel_mean(tif_file):
    """提取有效像元的平均值"""
    try:
        with rasterio.open(tif_file) as src:
            data = src.read(1)
            nodata = src.nodata

            # 提取有效像元（非nodata值且非NaN值）
            if nodata is not None:
                # 同时排除nodata值和NaN值
                valid_mask = (data != nodata) & (~np.isnan(data)) & (data > -9999)
                valid_data = data[valid_mask]
            else:
                # 只排除NaN值和极值
                valid_mask = (~np.isnan(data)) & (data > -9999) & (data < 1e10)
                valid_data = data[valid_mask]

            if len(valid_data) > 0:
                mean_value = np.nanmean(valid_data)  # 使用nanmean确保忽略任何残留的NaN
                logger.info(f"文件 {os.path.basename(tif_file)}: 有效像元数={len(valid_data)}, 平均值={mean_value:.4f}")
                return mean_value
            else:
                logger.warning(f"文件 {tif_file} 没有有效像元")
                return np.nan

    except Exception as e:
        logger.error(f"处理文件 {tif_file} 失败: {e}")
        return np.nan

def get_historical_baseline(historical_path):
    """获取历史多年平均基准值"""
    rx1day_file = os.path.join(historical_path, "RX1-day_1971_2020_mean.tif")
    
    if not os.path.exists(rx1day_file):
        logger.error(f"历史RX1-day文件不存在: {rx1day_file}")
        return None
    
    baseline = extract_valid_pixel_mean(rx1day_file)
    logger.info(f"历史基准值 (1971-2020): {baseline:.4f}")
    return baseline

def extract_future_data(future_base_path, scenarios, statistics, years):
    """提取未来数据"""
    results = {}
    
    for scenario in scenarios:
        results[scenario] = {}
        for stat in statistics:
            results[scenario][stat] = {}
            
            stat_path = os.path.join(future_base_path, scenario, stat, "RX1-day")
            
            if not os.path.exists(stat_path):
                logger.warning(f"路径不存在: {stat_path}")
                continue
            
            for year in years:
                rx1day_file = os.path.join(stat_path, f"RX1-day_{year}.tif")
                
                if os.path.exists(rx1day_file):
                    mean_value = extract_valid_pixel_mean(rx1day_file)
                    results[scenario][stat][year] = mean_value
                else:
                    logger.warning(f"文件不存在: {rx1day_file}")
                    results[scenario][stat][year] = np.nan
    
    return results

def calculate_growth_rates(future_data, baseline, scenarios):
    """计算增长率"""
    growth_rates = {}

    for scenario in scenarios:
        growth_rates[scenario] = {}

        for stat in ['Mean', 'P95', 'P05']:
            if stat in future_data[scenario]:
                growth_rates[scenario][stat] = {}

                for year, value in future_data[scenario][stat].items():
                    # 检查值和基准值都是有效的
                    if not np.isnan(value) and baseline is not None and not np.isnan(baseline) and baseline != 0:
                        growth_rate = (value - baseline) / baseline * 100  # 转换为百分比
                        growth_rates[scenario][stat][year] = growth_rate
                        logger.debug(f"{scenario} {stat} {year}: 未来值={value:.4f}, 基准值={baseline:.4f}, 增长率={growth_rate:.2f}%")
                    else:
                        growth_rates[scenario][stat][year] = np.nan
                        if np.isnan(value):
                            logger.warning(f"{scenario} {stat} {year}: 未来值为NaN")
                        if baseline is None or np.isnan(baseline):
                            logger.warning(f"基准值无效: {baseline}")

    return growth_rates

def create_time_series_plot(growth_rates, scenarios, output_path):
    """创建时间序列图"""
    # 设置图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    # 年份列表
    years = list(range(2025, 2101, 5))  # 2025, 2030, ..., 2100
    
    colors = {'ssp126': '#1f77b4', 'ssp245': '#ff7f0e', 'ssp370': '#2ca02c', 'ssp585': '#d62728'}
    
    for i, scenario in enumerate(scenarios):
        ax = axes[i]
        
        if scenario in growth_rates:
            # 提取数据
            mean_values = [growth_rates[scenario].get('Mean', {}).get(year, np.nan) for year in years]
            p95_values = [growth_rates[scenario].get('P95', {}).get(year, np.nan) for year in years]
            p05_values = [growth_rates[scenario].get('P05', {}).get(year, np.nan) for year in years]

            # 转换为numpy数组
            mean_array = np.array(mean_values, dtype=float)
            p95_array = np.array(p95_values, dtype=float)
            p05_array = np.array(p05_values, dtype=float)

            # 找到至少有Mean值的有效数据点
            valid_mean_indices = ~np.isnan(mean_array)

            if np.any(valid_mean_indices):
                # 对于Mean值有效的点，绘制主线
                valid_years_mean = np.array(years)[valid_mean_indices]
                valid_mean = mean_array[valid_mean_indices]

                # 对于置信区间，只在P95和P05都有效时绘制
                valid_interval_indices = ~(np.isnan(p95_array) | np.isnan(p05_array))

                logger.info(f"{scenario}: 有效Mean数据点={np.sum(valid_mean_indices)}, 有效区间数据点={np.sum(valid_interval_indices)}")
            else:
                logger.warning(f"{scenario}: 没有有效的Mean数据点")
                continue
            
            # 绘制主线（平均值）
            if len(valid_years_mean) > 0:
                ax.plot(valid_years_mean, valid_mean, color=colors[scenario], linewidth=2,
                       label=f'{scenario.upper()} Mean', marker='o', markersize=4)

            # 绘制置信区间（只在有有效区间数据时）
            if np.any(valid_interval_indices):
                valid_years_interval = np.array(years)[valid_interval_indices]
                valid_p95_interval = p95_array[valid_interval_indices]
                valid_p05_interval = p05_array[valid_interval_indices]

                ax.fill_between(valid_years_interval, valid_p05_interval, valid_p95_interval,
                               color=colors[scenario], alpha=0.3,
                               label=f'{scenario.upper()} P05-P95')

                # 绘制上下边界线
                ax.plot(valid_years_interval, valid_p95_interval, color=colors[scenario],
                       linestyle='--', alpha=0.7, linewidth=1)
                ax.plot(valid_years_interval, valid_p05_interval, color=colors[scenario],
                       linestyle='--', alpha=0.7, linewidth=1)
        
        # 设置图形属性
        ax.set_title(f'{scenario.upper()} RX1-day增长率', fontsize=14, fontweight='bold')
        ax.set_xlabel('年份', fontsize=12)
        ax.set_ylabel('增长率 (%)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 设置x轴
        ax.set_xlim(2020, 2105)
        ax.set_xticks(range(2025, 2101, 15))
        
        # 添加零线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=0.8)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    logger.info(f"时间序列图已保存到: {output_path}")

def validate_data(future_data, baseline, scenarios):
    """验证数据并输出统计信息"""
    logger.info("=" * 50)
    logger.info("数据验证报告:")
    logger.info(f"历史基准值: {baseline}")

    for scenario in scenarios:
        if scenario in future_data:
            logger.info(f"\n{scenario.upper()}:")
            for stat in ['Mean', 'P95', 'P05']:
                if stat in future_data[scenario]:
                    values = list(future_data[scenario][stat].values())
                    valid_count = sum(1 for v in values if not np.isnan(v))
                    total_count = len(values)
                    logger.info(f"  {stat}: {valid_count}/{total_count} 有效数据点")
                    if valid_count > 0:
                        valid_values = [v for v in values if not np.isnan(v)]
                        logger.info(f"    范围: {min(valid_values):.4f} - {max(valid_values):.4f}")
    logger.info("=" * 50)

def main():
    """主函数"""
    logger.info("开始RX1-day增长率分析...")

    # 设置路径
    historical_path, future_base_path = setup_paths()

    # 定义参数
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    statistics = ['Mean', 'P95', 'P05']
    years = list(range(2025, 2101, 5))  # 2025, 2030, ..., 2100
    
    # 获取历史基准值
    logger.info("提取历史基准值...")
    baseline = get_historical_baseline(historical_path)
    
    if baseline is None:
        logger.error("无法获取历史基准值，程序终止")
        return
    
    # 提取未来数据
    logger.info("提取未来数据...")
    future_data = extract_future_data(future_base_path, scenarios, statistics, years)

    # 验证数据
    validate_data(future_data, baseline, scenarios)

    # 计算增长率
    logger.info("计算增长率...")
    growth_rates = calculate_growth_rates(future_data, baseline, scenarios)
    
    # 创建输出目录
    output_dir = r"Z:/yuan/paper3_new02/EPEs_plot/EPEs_growth"
    os.makedirs(output_dir, exist_ok=True)

    # 保存结果到CSV
    logger.info("保存结果...")
    results_df = []
    valid_data_count = 0

    for scenario in scenarios:
        for stat in ['Mean', 'P95', 'P05']:
            if scenario in growth_rates and stat in growth_rates[scenario]:
                for year, rate in growth_rates[scenario][stat].items():
                    # 只保存有效的增长率数据
                    if not np.isnan(rate):
                        results_df.append({
                            'Scenario': scenario,
                            'Statistic': stat,
                            'Year': year,
                            'Growth_Rate_Percent': rate
                        })
                        valid_data_count += 1
                    else:
                        # 也保存NaN数据，但标记为无效
                        results_df.append({
                            'Scenario': scenario,
                            'Statistic': stat,
                            'Year': year,
                            'Growth_Rate_Percent': 'NaN'
                        })

    df = pd.DataFrame(results_df)
    csv_output = os.path.join(output_dir, "rx1day_growth_rates.csv")
    df.to_csv(csv_output, index=False, encoding='utf-8-sig')
    logger.info(f"结果已保存到: {csv_output}")
    logger.info(f"有效数据点数量: {valid_data_count}/{len(results_df)}")

    # 创建时间序列图
    logger.info("绘制时间序列图...")
    plot_output = os.path.join(output_dir, "rx1day_growth_timeseries.png")
    create_time_series_plot(growth_rates, scenarios, plot_output)
    
    logger.info("分析完成!")

if __name__ == "__main__":
    main()
