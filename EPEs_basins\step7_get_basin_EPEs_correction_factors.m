clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 定义模型
models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};


% 设置输入目录
input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_EPEs';

% 读取第一年数据获取流域数量
first_file = fullfile(input_dir, 'EPEs_1971.csv');
T_first = readtable(first_file);
num_basins = size(T_first, 1);

% 初始化存储所有年份数据的矩阵
yearly_data = zeros(44, num_basins, length(HW_indexs)); % 1971-2014年,共44年
years = 1971:2014;

% 循环读取每年的数据
for i = 1:length(years)
    y = years(i);
    y_str = num2str(y);
    
    % 构建输入文件路径
    input_file = fullfile(input_dir, ['EPEs_', y_str, '.csv']);
    
    % 读取CSV文件
    T = readtable(input_file);
    
    % 存储数据 - 保持流域维度
    yearly_data(i,:,:) = table2array(T);
end

% 计算多年平均值 - 对年份维度取平均
historical_means = squeeze(mean(yearly_data, 1));

fprintf('已完成历史数据的读取\n');

% 读取观测数据的多年平均值
obs_means = historical_means;

% 设置校正系数的上下限
min_correction = 0.1;  % 最小校正系数
max_correction = 10.0; % 最大校正系数

% 循环处理每个模型
for m_idx = 1:length(models)
    model = models{m_idx};
    
    % 设置输入输出路径
    input_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_EPEs_models/%s', model);
    
    % 初始化存储该模式所有年份数据的矩阵
    model_yearly_data = zeros(length(years), num_basins, length(HW_indexs));
    
    % 循环读取每年的数据
    for i = 1:length(years)
        y = years(i);
        y_str = num2str(y);
        
        % 构建输入文件路径
        input_file = fullfile(input_dir, ['EPEs_', y_str, '.csv']);
        
        % 读取CSV文件
        T = readtable(input_file);
        
        % 存储数据 - 保持流域维度
        model_yearly_data(i,:,:) = table2array(T);
    end
    
    % 计算模式的多年平均值 - 对年份维度取平均
    model_means = squeeze(mean(model_yearly_data, 1));
    
    % 初始化校正系数数组
    correction_factors = zeros(num_basins, length(HW_indexs));
    
    % 计算每个流域的校正系数并进行限制
    for basin = 1:num_basins
        for idx = 1:length(HW_indexs)
            if model_means(basin,idx) > 0 && obs_means(basin,idx) > 0
                % 正常计算校正系数
                cf = obs_means(basin,idx) / model_means(basin,idx);
                
                % 限制校正系数在设定范围内
                cf = max(min_correction, min(cf, max_correction));
                
                correction_factors(basin,idx) = cf;
            elseif model_means(basin,idx) == 0 && obs_means(basin,idx) == 0
                % 如果两者都为0，设置校正系数为1
                correction_factors(basin,idx) = 1;
            elseif model_means(basin,idx) == 0
                % 如果模式值为0但观测值不为0，设置为最大校正系数
                correction_factors(basin,idx) = max_correction;
            else
                % 如果观测值为0但模式值不为0，设置为最小校正系数
                correction_factors(basin,idx) = min_correction;
            end
        end
    end
    
    % 创建校正系数表格
    correction_table = array2table(correction_factors, 'VariableNames', HW_indexs);
    
    % 保存校正系数
    output_file = fullfile(input_dir, 'correction_factors.csv');
    writetable(correction_table, output_file);
    
    fprintf('已完成%s模式的校正系数计算和保存\n', model);
end

fprintf('已完成所有模式的校正系数计算\n');