clc;clear;close all

addpath '/mnt/sdb2/yuanshuai/wanpan/yuan/ubuntu_code'

HW_indexs = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};

% 定义模型
models = {'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0','NorESM2-LM','NorESM2-MM','TaiESM1'};

% 设置输入和输出目录
yz_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_yz';

% 读取阈值数据
yz_file = fullfile(yz_dir, 'basin_precipitation_thresholds2.csv');
yz_table = readtable(yz_file);

% 获取阈值数据
pre_yz = table2array(yz_table(:,{'P90','P95','P99'}));

fprintf('已读取流域降水阈值数据\n');

% 循环处理每个模型
for m_idx = 1:length(models)
    model = models{m_idx};
    
    % 设置输入输出路径
    input_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_daily_mean_models/%s', model);
    output_dir = sprintf('/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_EPEs_models/%s', model);
    
    % 如果输出目录不存在则创建
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end
    
    % 初始化存储所有年份数据的数组
    basin_ids = [];
    
    % 循环处理每一年的数据
    for y = 1971:2014
        y_str = num2str(y);
        
        % 构建输入CSV文件路径
        csv_filename = fullfile(input_dir, sprintf('basin_daily_means_%s.csv', y_str));
        
        % 读取CSV文件
        T = readtable(csv_filename);
        
        % 获取流域ID（只在第一年获取）
        if y == 1971
            basin_ids = T.BasinID;
        end
        
        % 获取日降水数据(不包括BasinID列)
        daily_data = table2array(T(:,2:end));
        
        [outdata] = compute_EX_pr_mon_8index_basins(daily_data,pre_yz);
        
        % 创建表头
        headers = {'R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day'};
        
        % 将数据转换为表格
        T = array2table(outdata, 'VariableNames', headers);
        
        % 构建输出文件路径
        output_file = fullfile(output_dir, ['EPEs_', y_str, '.csv']);
        
        % 将表格写入CSV文件
        writetable(T, output_file);
        
        fprintf('已完成%s模式%d年数据处理\n', model, y);
    end